import {
  redirect,
  type LoaderFunctionArgs,
  type MetaFunction,
} from '@shopify/remix-oxygen';
import {useLoaderData, Link} from 'react-router';
import {
  Image,
  getSelectedProductOptions,
  Analytics,
  useOptimisticVariant,
  useSelectedOptionInUrlParam,
  getProductOptions,
} from '@shopify/hydrogen';
import {useState, useEffect} from 'react';
import {AddToCartButton} from '~/components/AddToCartButton';
import {useAside} from '~/components/Aside';
import {useNavigate} from 'react-router';
import {CrossSellProducts} from '~/components/CrossSellProducts';
import {Money} from '@shopify/hydrogen';
import {Suspense} from 'react';
import {Await} from 'react-router';

export const meta: MetaFunction<typeof loader> = () => {
  return [{title: `First One Free - Coffee Subscription | Big River Coffee`}];
};

export async function loader({request, context}: LoaderFunctionArgs) {
  const {storefront} = context;

  const selectedOptions = getSelectedProductOptions(request).filter(
    (option) =>
      // Filter out Shopify predictive search query params
      !option.name.startsWith('_sid') &&
      !option.name.startsWith('_pos') &&
      !option.name.startsWith('_psq') &&
      !option.name.startsWith('_ss') &&
      !option.name.startsWith('_v') &&
      // Filter out pagination query params
      !option.name.startsWith('cursor'),
  );

  // Query the specific subscription promotion product by ID
  const {product} = await storefront.query(SUBSCRIPTION_PROMO_PRODUCT_QUERY, {
    variables: {
      id: 'gid://shopify/Product/10132826423611',
      selectedOptions,
    },
  });

  if (!product?.id) {
    throw new Response(null, {status: 404});
  }

  // Fetch bundle products and related products for cross-selling
  const relatedProducts = storefront
    .query(RELATED_PRODUCTS_QUERY, {
      variables: {first: 20},
      cache: storefront.CacheLong(),
    })
    .catch((error) => {
      console.error('Error fetching related products:', error);
      return {products: {nodes: []}};
    });

  // Fetch specific bundle products
  const bundleProducts = storefront
    .query(BUNDLE_PRODUCTS_QUERY, {
      variables: {
        roastersBoxId: 'gid://shopify/Product/10111587647803',
        blendBoxId: 'gid://shopify/Product/10111589941563',
      },
      cache: storefront.CacheLong(),
    })
    .catch((error) => {
      console.error('Error fetching bundle products:', error);
      return {roastersBox: null, blendBox: null};
    });

  const firstVariant = product.variants.nodes[0];
  const firstVariantIsDefault = Boolean(
    firstVariant.selectedOptions.find((option: any) => option.value === 'Default Title'),
  );

  if (firstVariantIsDefault) {
    product.selectedVariant = firstVariant;
  } else {
    // if no selected variant was returned from the selected options,
    // we redirect to the first variant's URL with it's selected options applied
    if (!product.selectedVariant) {
      throw redirectToFirstVariant({product, request});
    }
  }

  // In order to show which variants are available in the UI, we need to query
  // all of them. But there might be a *lot*, so instead separate the variants
  // into it's own separate query that is deferred. So there's a brief moment
  // where variant options might show as available when they're not, but after
  // this deffered query resolves, the UI will update.
  const variants = storefront.query(VARIANTS_QUERY, {
    variables: {id: product.id},
  });

  const productOptions = getProductOptions(product);

  return {
    product,
    variants,
    productOptions,
    relatedProducts,
    bundleProducts,
  };
}

function redirectToFirstVariant({
  product,
  request,
}: {
  product: any;
  request: Request;
}) {
  const url = new URL(request.url);
  const firstVariant = product.variants.nodes[0];

  return redirect(
    getVariantUrl({
      pathname: url.pathname,
      selectedOptions: firstVariant.selectedOptions,
      searchParams: url.searchParams,
    }),
    {
      status: 302,
    },
  );
}

function getVariantUrl({
  pathname,
  searchParams,
  selectedOptions,
}: {
  pathname: string;
  searchParams: URLSearchParams;
  selectedOptions: any[];
}) {
  const params = new URLSearchParams(searchParams);

  selectedOptions.forEach((option) => {
    params.set(option.name, option.value);
  });

  return `${pathname}?${params.toString()}`;
}

// Custom form component for subscription promotion
function SubscriptionPromoForm({
  productOptions,
  selectedVariant,
  product,
  showSubscriptionOnly = false,
}: {
  productOptions: any[];
  selectedVariant: any;
  product: any;
  showSubscriptionOnly?: boolean;
}) {
  const {open} = useAside();

  const [selectedSellingPlanId, setSelectedSellingPlanId] = useState<string | null>('gid://shopify/SellingPlan/9581986107'); // Default to monthly

  // Subscription options for this promotion
  const subscriptionOptions = [
    {
      id: 'weekly',
      name: 'Weekly',
      description: 'Delivered every week',
      sellingPlanId: 'gid://shopify/SellingPlan/9581953339',
    },
    {
      id: 'monthly',
      name: 'Monthly',
      description: 'Delivered every month',
      sellingPlanId: 'gid://shopify/SellingPlan/9581986107',
    },
    {
      id: 'every3weeks',
      name: 'Every 3 weeks',
      description: 'Delivered every 3 weeks',
      sellingPlanId: 'gid://shopify/SellingPlan/9582018875',
    },
    {
      id: 'every6weeks',
      name: 'Every 6 weeks',
      description: 'Delivered every 6 weeks',
      sellingPlanId: 'gid://shopify/SellingPlan/9582051643',
    },
  ];

  if (showSubscriptionOnly) {
    return (
      <div className="space-y-4">
        {/* Subscription Frequency Selection */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gradient-to-r from-[#db8027] to-[#c4721f] rounded-full shadow-sm"></div>
            <h4 className="text-sm font-bold !text-[#db8027] uppercase tracking-wide">🚚 Delivery Schedule</h4>
          </div>

          <div className="grid grid-cols-1 gap-2">
            {subscriptionOptions.map((option) => (
              <button
                key={option.id}
                type="button"
                onClick={() => setSelectedSellingPlanId(option.sellingPlanId)}
                className={`w-full p-3 rounded-lg text-sm font-semibold transition-all duration-200 border-2 ${
                  selectedSellingPlanId === option.sellingPlanId
                    ? 'bg-gradient-to-r from-[#db8027] to-[#c4721f] text-white border-[#db8027] shadow-lg transform scale-105'
                    : 'bg-white text-gray-700 hover:bg-orange-50 border-gray-300 hover:border-[#db8027] hover:shadow-md hover:scale-102'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="text-left">
                    <div className="font-bold">{option.name}</div>
                    <div className="text-xs opacity-90">{option.description} • 15% off</div>
                  </div>
                  {selectedSellingPlanId === option.sellingPlanId && (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Add to Cart Button */}
        <div className="space-y-3">
          <AddToCartButton
            disabled={!selectedVariant || !selectedVariant.availableForSale}
            onClick={() => open('cart')}
            lines={
              selectedVariant
                ? [
                    {
                      merchandiseId: selectedVariant.id,
                      quantity: 1, // Fixed quantity of 1 for this promotion
                      selectedVariant,
                      sellingPlanId: selectedSellingPlanId,
                    },
                  ]
                : []
            }
            className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-4 px-6 rounded-xl text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl transform"
          >
            <div className="flex items-center justify-center space-x-2">
              <span>🎉</span>
              <span>Get Your FREE Coffee Now!</span>
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"/>
              </svg>
            </div>
          </AddToCartButton>

          {/* Benefits */}
          <div className="bg-green-50 rounded-lg p-3 border border-green-200">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs text-green-800">
              <div className="flex items-center space-x-1">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>No commitment</span>
              </div>
              <div className="flex items-center space-x-1">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>Cancel anytime</span>
              </div>
              <div className="flex items-center space-x-1">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>Free shipping $30+</span>
              </div>
              <div className="flex items-center space-x-1">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>15% off every order</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show product options (flavor, size, grind, etc.) for the top section
  return (
    <div className="space-y-4">
      {/* Debug info */}
      {productOptions.length === 0 && (
        <div className="text-sm text-gray-500 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
          No product options found. This might be a single-variant product.
        </div>
      )}

      {productOptions.map((option) => {
        // If there is only a single value in the option values, don't display the option
        if (option.optionValues.length === 1) return null;

        // Special handling for flavor options
        const isFlavor = option.name.toLowerCase().includes('flavor') ||
                        option.name.toLowerCase().includes('title') ||
                        option.name.toLowerCase().includes('variant');

        return (
          <div className="space-y-2" key={option.name}>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full shadow-sm ${
                isFlavor
                  ? 'bg-gradient-to-r from-[#db8027] to-[#c4721f]'
                  : 'bg-gradient-to-r from-[#10b981] to-[#059669]'
              }`}></div>
              <h4 className={`text-sm font-bold uppercase tracking-wide ${
                isFlavor ? '!text-[#db8027]' : '!text-[#047857]'
              }`}>
                {isFlavor ? '☕ Choose Your Flavor' : option.name}
              </h4>
            </div>

            {isFlavor ? (
              // Special grid layout for flavors
              <div className="grid grid-cols-1 gap-2">
                {option.optionValues.map((value: any) => {
                  const {
                    name,
                    variantUriQuery,
                    selected,
                    available,
                    exists,
                    isDifferentProduct,
                  } = value;

                  const baseClasses = `w-full p-3 rounded-lg text-sm font-semibold transition-all duration-200 border-2 ${
                    selected
                      ? 'bg-gradient-to-r from-[#db8027] to-[#c4721f] text-white border-[#db8027] shadow-lg transform scale-105'
                      : 'bg-white text-gray-700 hover:bg-orange-50 border-gray-300 hover:border-[#db8027] hover:shadow-md hover:scale-102'
                  } ${!available || !exists ? 'opacity-40 cursor-not-allowed' : 'cursor-pointer'}`;

                  if (isDifferentProduct) {
                    return (
                      <Link
                        className={baseClasses}
                        key={option.name + name}
                        prefetch="intent"
                        preventScrollReset
                        replace
                        to={`/products/first-one-free?${variantUriQuery}`}
                      >
                        <div className="flex items-center justify-between">
                          <span>{name}</span>
                          {selected && (
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      </Link>
                    );
                  } else {
                    return (
                      <button
                        type="button"
                        className={baseClasses}
                        key={option.name + name}
                        disabled={!exists}
                        onClick={() => {
                          if (!selected) {
                            window.location.href = `/products/first-one-free?${variantUriQuery}`;
                          }
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <span>{name}</span>
                          {selected && (
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      </button>
                    );
                  }
                })}
              </div>
            ) : (
              // Regular compact layout for other options
              <div className="flex flex-wrap gap-2">
                {option.optionValues.map((value: any) => {
                  const {
                    name,
                    variantUriQuery,
                    selected,
                    available,
                    exists,
                    isDifferentProduct,
                  } = value;

                  const baseClasses = `px-3 py-2 rounded-lg text-xs font-semibold transition-all duration-200 border ${
                    selected
                      ? 'bg-gradient-to-r from-[#10b981] to-[#059669] text-white border-[#10b981] shadow-md'
                      : 'bg-white text-gray-700 hover:bg-emerald-50 border-gray-300 hover:border-[#10b981]'
                  } ${!available || !exists ? 'opacity-40 cursor-not-allowed' : 'cursor-pointer'}`;

                  if (isDifferentProduct) {
                    return (
                      <Link
                        className={baseClasses}
                        key={option.name + name}
                        prefetch="intent"
                        preventScrollReset
                        replace
                        to={`/products/first-one-free?${variantUriQuery}`}
                      >
                        {name}
                      </Link>
                    );
                  } else {
                    return (
                      <button
                        type="button"
                        className={baseClasses}
                        key={option.name + name}
                        disabled={!exists}
                        onClick={() => {
                          if (!selected) {
                            window.location.href = `/products/first-one-free?${variantUriQuery}`;
                          }
                        }}
                      >
                        {name}
                      </button>
                    );
                  }
                })}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}

// Compact form component with tabs for subscription vs one-time purchase
function CompactProductForm({
  productOptions,
  selectedVariant,
  product,
  updateImageForVariant,
}: {
  productOptions: any[];
  selectedVariant: any;
  product: any;
  updateImageForVariant: (variantId: string) => void;
}) {
  const {open} = useAside();
  const navigate = useNavigate();

  const [selectedSellingPlanId, setSelectedSellingPlanId] = useState<string | null>('gid://shopify/SellingPlan/9581986107'); // Default to monthly
  const [activeTab, setActiveTab] = useState<'subscription' | 'onetime'>('subscription');

  // Ensure selling plan is set when switching to subscription
  useEffect(() => {
    if (activeTab === 'subscription' && !selectedSellingPlanId) {
      setSelectedSellingPlanId('gid://shopify/SellingPlan/9581986107'); // Default to monthly
    }
  }, [activeTab, selectedSellingPlanId]);

  const subscriptionOptions = [
    {
      id: 'weekly',
      name: 'Weekly',
      description: '15% off',
      sellingPlanId: 'gid://shopify/SellingPlan/9581953339',
    },
    {
      id: 'monthly',
      name: 'Monthly',
      description: '15% off',
      sellingPlanId: 'gid://shopify/SellingPlan/9581986107',
    },
    {
      id: 'every3weeks',
      name: 'Every 3 weeks',
      description: '15% off',
      sellingPlanId: 'gid://shopify/SellingPlan/9582018875',
    },
    {
      id: 'every6weeks',
      name: 'Every 6 weeks',
      description: '15% off',
      sellingPlanId: 'gid://shopify/SellingPlan/9582051643',
    },
  ];

  return (
    <div className="space-y-3">
      {/* Purchase Type Tabs */}
      <div className="flex bg-gray-100 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('subscription')}
          className={`flex-1 py-1.5 px-2 rounded-md text-xs font-medium transition-all ${
            activeTab === 'subscription'
              ? 'bg-white text-orange-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          🎉 FREE Coffee
        </button>
        <button
          onClick={() => setActiveTab('onetime')}
          className={`flex-1 py-1.5 px-2 rounded-md text-xs font-medium transition-all ${
            activeTab === 'onetime'
              ? 'bg-white text-gray-800 shadow-sm'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          💰 One-Time
        </button>
      </div>

      {/* Product Options - Compact Layout */}
      <div className="space-y-2">
        <h3 className="text-xs font-bold text-gray-800">Customize Your Coffee</h3>

        {productOptions.map((option) => {
          const isFlavor = option.name === 'Flavor';

          return (
            <div key={option.name} className="space-y-1">
              <label className="text-xs font-medium text-orange-600 uppercase tracking-wide">
                {isFlavor ? '☕ Flavor' :
                 option.name === 'Size' ? '📏 Size' :
                 option.name === 'Grind' ? '⚙️ Grind' : option.name}
              </label>

              {isFlavor ? (
                // Special grid layout for flavors with buttons
                <div className="grid grid-cols-2 gap-1">
                  {option.optionValues.map((value: any) => {
                    const {
                      name,
                      available,
                      exists,
                      variantUriQuery,
                      selected,
                    } = value;

                    const baseClasses = `px-2 py-1.5 rounded-lg text-xs font-semibold transition-all duration-200 border-2 ${
                      selected
                        ? 'bg-gradient-to-r from-[#db8027] to-[#c4721f] text-white border-[#db8027] shadow-lg transform scale-105'
                        : 'bg-white text-gray-700 hover:bg-orange-50 border-gray-300 hover:border-[#db8027] hover:shadow-md hover:scale-[1.02]'
                    } ${!available || !exists ? 'opacity-40 cursor-not-allowed' : 'cursor-pointer'}`;

                    return (
                      <button
                        type="button"
                        className={baseClasses}
                        key={option.name + name}
                        disabled={!exists}
                        onClick={() => {
                          if (!selected) {
                            // Find the variant that matches this flavor selection
                            const targetVariant = product.variants?.nodes?.find((variant: any) => {
                              return variant.selectedOptions?.some((opt: any) =>
                                opt.name === 'Flavor' && opt.value === name
                              );
                            });

                            // Update the image if we found a matching variant
                            if (targetVariant?.id) {
                              updateImageForVariant(targetVariant.id);
                            }

                            navigate(`?${variantUriQuery}`, {
                              replace: true,
                              preventScrollReset: true,
                            });
                          }
                        }}
                      >
                        {name}
                      </button>
                    );
                  })}
                </div>
              ) : (
                // Dropdown for other options (like grind)
                <select
                  value={option.optionValues.find((v: any) => v.selected)?.name || ''}
                  onChange={(e) => {
                    const selectedValue = option.optionValues.find((v: any) => v.name === e.target.value);
                    if (selectedValue && !selectedValue.selected) {
                      navigate(`?${selectedValue.variantUriQuery}`, {
                        replace: true,
                        preventScrollReset: true,
                      });
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 text-xs focus:outline-none focus:ring-2 focus:ring-[#db8027] focus:border-[#db8027] transition-colors duration-200 hover:border-gray-400"
                >
                  {option.optionValues.map((value: any) => (
                    <option key={value.name} value={value.name}>{value.name}</option>
                  ))}
                </select>
              )}
            </div>
          );
        })}
      </div>

      {/* Subscription/Purchase Options */}
      {activeTab === 'subscription' ? (
        <div className="space-y-2">
          <label className="text-xs font-medium text-orange-600 uppercase tracking-wide">
            🚚 Delivery Schedule
          </label>

          <div className="grid grid-cols-2 gap-1">
            {subscriptionOptions.map((plan) => (
              <button
                key={plan.id}
                type="button"
                onClick={() => setSelectedSellingPlanId(plan.sellingPlanId)}
                className={`p-2 rounded-lg text-xs font-semibold transition-all duration-200 border-2 ${
                  selectedSellingPlanId === plan.sellingPlanId
                    ? 'bg-gradient-to-r from-[#db8027] to-[#c4721f] text-white border-[#db8027] shadow-lg transform scale-105'
                    : 'bg-white text-gray-700 hover:bg-orange-50 border-gray-300 hover:border-[#db8027] hover:shadow-md hover:scale-[1.02]'
                }`}
              >
                <div className="text-center">
                  <div className="font-bold">{plan.name}</div>
                  <div className="text-xs opacity-90">{plan.description}</div>
                </div>
              </button>
            ))}
          </div>

          <AddToCartButton
            disabled={!selectedVariant || !selectedVariant.availableForSale}
            onClick={() => open('cart')}
            lines={
              selectedVariant
                ? [
                    {
                      merchandiseId: selectedVariant.id,
                      quantity: 1,
                      selectedVariant,
                      ...(activeTab === 'subscription' && selectedSellingPlanId && {
                        sellingPlanId: selectedSellingPlanId,
                      }),
                    },
                  ]
                : []
            }
            className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white font-bold py-2.5 px-4 rounded-xl hover:from-green-600 hover:to-green-700 transform hover:scale-105 transition-all duration-200 shadow-lg text-sm"
          >
            ADD TO CART
          </AddToCartButton>
        </div>
      ) : (
        <div className="space-y-2">
          <div className="bg-gray-50 p-2 rounded-lg">
            <div className="text-base font-bold text-gray-800">$19.99</div>
            <div className="text-xs text-gray-600">One-time purchase</div>
          </div>

          <AddToCartButton
            disabled={!selectedVariant || !selectedVariant.availableForSale}
            onClick={() => open('cart')}
            lines={
              selectedVariant
                ? [
                    {
                      merchandiseId: selectedVariant.id,
                      quantity: 1,
                      selectedVariant,
                    },
                  ]
                : []
            }
            className="w-full bg-gradient-to-r from-gray-700 to-gray-800 text-white font-bold py-2.5 px-4 rounded-xl hover:from-gray-800 hover:to-gray-900 transform hover:scale-105 transition-all duration-200 shadow-lg text-sm"
          >
            Add to Cart
          </AddToCartButton>

          <div className="text-xs text-gray-600 text-center">
            💡 Free bag with a subscription above!
          </div>
        </div>
      )}
    </div>
  );
}

// Bundle Section Component
function BundleSection({bundleProducts}: {bundleProducts: any}) {
  return (
    <Suspense fallback={
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Loading placeholders */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 animate-pulse">
          <div className="flex items-start space-x-4">
            <div className="w-20 h-20 rounded-lg bg-white/20"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-white/20 rounded w-3/4"></div>
              <div className="h-6 bg-white/20 rounded w-1/2"></div>
              <div className="h-3 bg-white/20 rounded w-full"></div>
            </div>
          </div>
        </div>
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 animate-pulse">
          <div className="flex items-start space-x-4">
            <div className="w-20 h-20 rounded-lg bg-white/20"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-white/20 rounded w-3/4"></div>
              <div className="h-6 bg-white/20 rounded w-1/2"></div>
              <div className="h-3 bg-white/20 rounded w-full"></div>
            </div>
          </div>
        </div>
      </div>
    }>
      <Await resolve={bundleProducts}>
        {(resolvedBundles) => (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Roasters Box */}
            {resolvedBundles?.roastersBox && (
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="flex items-start space-x-4">
                  {resolvedBundles.roastersBox.featuredImage ? (
                    <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        data={resolvedBundles.roastersBox.featuredImage}
                        aspectRatio="1/1"
                        sizes="80px"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 bg-white/20 flex items-center justify-center">
                      <span className="text-2xl">🎯</span>
                    </div>
                  )}
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg mb-2">{resolvedBundles.roastersBox.title}</h3>
                    <div className="flex items-center justify-between mb-4">
                      <div className="text-2xl font-bold">
                        <Money data={resolvedBundles.roastersBox.priceRange.minVariantPrice} />
                      </div>
                      <div className="bg-green-500 text-white text-sm px-3 py-1 rounded-full font-bold">
                        Best Value
                      </div>
                    </div>
                    <p className="text-sm opacity-90 mb-4">Curated selection of our premium roasts</p>
                    <div className="flex flex-col sm:flex-row gap-2">
                      <Link
                        to={`/products/${resolvedBundles.roastersBox.handle}`}
                        className="flex-1 bg-white text-[#db8027] font-medium py-2 sm:py-2 px-3 sm:px-4 rounded-lg text-center hover:bg-gray-100 transition-colors text-xs sm:text-sm"
                      >
                        View Details
                      </Link>
                      <Link
                        to={`/products/${resolvedBundles.roastersBox.handle}`}
                        className="flex-1 bg-army-600 hover:bg-army-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-center text-xs sm:text-sm"
                      >
                        Shop Now
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Blend Box */}
            {resolvedBundles?.blendBox && (
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="flex items-start space-x-4">
                  {resolvedBundles.blendBox.featuredImage ? (
                    <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        data={resolvedBundles.blendBox.featuredImage}
                        aspectRatio="1/1"
                        sizes="80px"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 bg-white/20 flex items-center justify-center">
                      <span className="text-2xl">📦</span>
                    </div>
                  )}
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg mb-2">{resolvedBundles.blendBox.title}</h3>
                    <div className="flex items-center justify-between mb-4">
                      <div className="text-2xl font-bold">
                        <Money data={resolvedBundles.blendBox.priceRange.minVariantPrice} />
                      </div>
                      <div className="bg-blue-500 text-white text-sm px-3 py-1 rounded-full font-bold">
                        Popular
                      </div>
                    </div>
                    <p className="text-sm opacity-90 mb-4">Curated selection of our signature blends</p>
                    <div className="flex flex-col sm:flex-row gap-2">
                      <Link
                        to={`/products/${resolvedBundles.blendBox.handle}`}
                        className="flex-1 bg-white text-[#db8027] font-medium py-2 sm:py-2 px-3 sm:px-4 rounded-lg text-center hover:bg-gray-100 transition-colors text-xs sm:text-sm"
                      >
                        View Details
                      </Link>
                      <Link
                        to={`/products/${resolvedBundles.blendBox.handle}`}
                        className="flex-1 bg-army-600 hover:bg-army-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-center text-xs sm:text-sm"
                      >
                        Shop Now
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Fallback if no bundle products */}
            {(!resolvedBundles?.roastersBox && !resolvedBundles?.blendBox) && (
              <>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <div className="flex items-start space-x-4">
                    <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 bg-white/20 flex items-center justify-center">
                      <span className="text-2xl">🎯</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-2">Roasters Box</h3>
                      <div className="flex items-center justify-between mb-4">
                        <div className="text-2xl font-bold">$39.99</div>
                        <div className="bg-green-500 text-white text-sm px-3 py-1 rounded-full font-bold">
                          Best Value
                        </div>
                      </div>
                      <p className="text-sm opacity-90 mb-4">Curated selection of our premium roasts</p>
                      <div className="flex flex-col sm:flex-row gap-2">
                        <Link
                          to="/products/roasterss-box"
                          className="flex-1 bg-white text-[#db8027] font-medium py-2 sm:py-2 px-3 sm:px-4 rounded-lg text-center hover:bg-gray-100 transition-colors text-xs sm:text-sm"
                        >
                          View Details
                        </Link>
                        <Link
                          to="/products/roasterss-box"
                          className="flex-1 bg-army-600 hover:bg-army-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-center text-xs sm:text-sm"
                        >
                          Shop Now
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <div className="flex items-start space-x-4">
                    <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 bg-white/20 flex items-center justify-center">
                      <span className="text-2xl">📦</span>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-2">Blend Box</h3>
                      <div className="flex items-center justify-between mb-4">
                        <div className="text-2xl font-bold">$34.99</div>
                        <div className="bg-blue-500 text-white text-sm px-3 py-1 rounded-full font-bold">
                          Popular
                        </div>
                      </div>
                      <p className="text-sm opacity-90 mb-4">Curated selection of our signature blends</p>
                      <div className="flex flex-col sm:flex-row gap-2">
                        <Link
                          to="/products/blend-box"
                          className="flex-1 bg-white text-[#db8027] font-medium py-2 sm:py-2 px-3 sm:px-4 rounded-lg text-center hover:bg-gray-100 transition-colors text-xs sm:text-sm"
                        >
                          View Details
                        </Link>
                        <Link
                          to="/products/blend-box"
                          className="flex-1 bg-army-600 hover:bg-army-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-center text-xs sm:text-sm"
                        >
                          Shop Now
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </Await>
    </Suspense>
  );
}

export default function FirstOneFreePromotion() {
  const {product, variants, productOptions, relatedProducts, bundleProducts} = useLoaderData<typeof loader>();

  // State for selected image
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Optimistically selects a variant with given available variant information
  const selectedVariant = useOptimisticVariant(
    product.selectedVariant || product.variants?.nodes?.[0],
    variants,
  );

  // Sets the search param to the selected variant without navigation
  // only when no search params are set in the url
  useSelectedOptionInUrlParam(selectedVariant?.selectedOptions || []);

  // Update image index when selected variant changes
  useEffect(() => {
    if (selectedVariant?.image?.id && product.images?.nodes) {
      const imageIndex = product.images.nodes.findIndex((img: any) => img.id === selectedVariant.image?.id);
      if (imageIndex !== -1) {
        setSelectedImageIndex(imageIndex);
      }
    }
  }, [selectedVariant?.image?.id, product.images?.nodes]);

  // Function to find and set the image for a specific variant
  const updateImageForVariant = (variantId: string) => {
    if (!product.variants?.nodes || !product.images?.nodes) return;

    // Find the variant
    const variant = product.variants.nodes.find((v: any) => v.id === variantId);
    if (!variant?.image?.id) return;

    // Find the corresponding image index
    const imageIndex = product.images.nodes.findIndex((img: any) => img.id === variant.image?.id);
    if (imageIndex !== -1) {
      setSelectedImageIndex(imageIndex);
    }
  };

  const {title, descriptionHtml} = product;

  // Safety check
  if (!selectedVariant) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen product-page-content" style={{
      backgroundImage: 'url(/newhomepage/mobile_homeage_bg_sf.webp), url(/newhomepage/mobile_homeage_bg_sf.png)',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }}>
      {/* Redesigned Hero Section */}
      <div className="relative bg-gradient-to-br from-[#3a5c5c]/95 to-[#2d4747]/95 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">

          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm text-white/70 mb-6">
            <Link to="/collections/all" className="hover:text-[#db8027] transition-colors">Coffee</Link>
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
            <span className="text-white/90">{title}</span>
          </nav>

          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 items-start">

            {/* Product Images - Left Column (3/5 width) */}
            <div className="lg:col-span-3 space-y-4">
              {product.images?.nodes?.length > 0 ? (
                <>
                  {/* Main Product Image */}
                  <div className="relative group">
                    <div className="aspect-square bg-white/10 rounded-2xl overflow-hidden backdrop-blur-sm border border-white/20 shadow-2xl">
                      <Image
                        alt={product.images.nodes[selectedImageIndex]?.altText || product.title}
                        data={product.images.nodes[selectedImageIndex] || product.featuredImage}
                        loading="eager"
                        sizes="(max-width: 1024px) 100vw, 33vw"
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                      />
                    </div>

                    {/* Floating Badges */}
                    <div className="absolute top-4 left-4 bg-[#db8027] text-white px-3 py-1.5 rounded-full text-sm font-bold shadow-lg">
                      Nicaragua
                    </div>
                    <div className="absolute top-4 right-4 bg-army-600 text-white px-3 py-1.5 rounded-full text-sm font-bold shadow-lg">
                      Premium
                    </div>

                    {/* FREE Badge */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-2 rounded-full text-lg font-bold shadow-xl animate-pulse">
                      🎉 FIRST ONE FREE
                    </div>
                  </div>

                  {/* Thumbnail Images */}
                  {product.images.nodes.length > 1 && (
                    <div className="flex space-x-3 overflow-x-auto pb-2">
                      {product.images.nodes.map((image: any, index: number) => (
                        <div
                          key={image.id}
                          className={`flex-shrink-0 w-20 h-20 rounded-xl overflow-hidden border-2 transition-all duration-200 cursor-pointer ${
                            selectedImageIndex === index
                              ? 'border-[#db8027] shadow-lg scale-105'
                              : 'border-white/30 hover:border-white/60 hover:scale-102'
                          }`}
                          onClick={() => setSelectedImageIndex(index)}
                        >
                          <Image
                            alt={image.altText || product.title}
                            data={image}
                            loading="lazy"
                            sizes="80px"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <div className="aspect-square bg-white/10 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/20">
                  <div className="text-center">
                    <svg className="w-16 h-16 text-white/60 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-white/60 text-sm">No image available</span>
                  </div>
                </div>
              )}
            </div>

            {/* Product Details - Right Column (2/5 width) */}
            <div className="lg:col-span-2 space-y-4">

              {/* Product Title & Price - Compact */}
              <div className="space-y-2">
                <div>
                  <p className="text-[#db8027] text-sm font-bold uppercase tracking-wide mb-1">{product.vendor}</p>
                  <h1 className="text-2xl lg:text-3xl font-bold text-white leading-tight mb-2">{title}</h1>
                  <div className="flex items-center gap-3 flex-wrap">
                    <div className="text-3xl lg:text-4xl font-bold text-green-400">
                      FREE
                      <span className="text-sm text-white/80 ml-2">First Order</span>
                    </div>

                  </div>
                </div>
              </div>

              {/* Compact Product Options & Purchase Form */}
              <div className="bg-white/95 backdrop-blur-sm rounded-xl p-3 border border-white/30 shadow-2xl">
                <CompactProductForm
                  productOptions={productOptions}
                  selectedVariant={selectedVariant as any}
                  product={product as any}
                  updateImageForVariant={updateImageForVariant}
                />
              </div>

            </div>
          </div>
        </div>
      </div>

      {/* Streamlined Product Benefits Section */}
      <div className="bg-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">

            {/* Product Description */}
            <div className="space-y-4">
              <div>
                <h2 className="text-2xl lg:text-3xl font-bold text-[#3a5c5c] mb-2">Start Your Coffee Journey FREE</h2>
                <div className="w-12 h-1 bg-[#db8027] mb-3"></div>
              </div>

              <div
                className="text-gray-700 leading-relaxed text-base"
                dangerouslySetInnerHTML={{__html: descriptionHtml}}
              />

              {/* Key Features */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-4">
                <div className="flex items-center space-x-2 p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-semibold text-green-800 text-sm">First Order FREE</div>
                    <div className="text-xs text-green-600">Just pay shipping</div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-semibold text-orange-800 text-sm">15% Off Future Orders</div>
                    <div className="text-xs text-orange-600">Ongoing savings</div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
                      <path d="M3 4a1 1 0 00-1 1v1a1 1 0 001 1h1l1.68 5.39a3 3 0 002.84 2.11h5.95a3 3 0 002.84-2.11L19.36 7H7.12l-.22-.72A3 3 0 004.04 4H3z"/>
                    </svg>
                  </div>
                  <div>
                    <div className="font-semibold text-blue-800 text-sm">Free Shipping</div>
                    <div className="text-xs text-blue-600">Orders over $30</div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 p-3 bg-purple-50 rounded-lg border border-purple-200">
                  <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-semibold text-purple-800 text-sm">No Commitment</div>
                    <div className="text-xs text-purple-600">Cancel anytime</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Promotional Image */}
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-green-100 to-green-200 rounded-2xl overflow-hidden shadow-lg">
                <img
                  src="/br_bundlepic.png"
                  alt="Coffee Subscription Bundle"
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Floating badges */}
              <div className="absolute -top-4 -right-4 bg-yellow-400 text-yellow-900 px-4 py-2 rounded-full text-sm font-bold shadow-lg transform rotate-12">
                LIMITED TIME!
              </div>
              <div className="absolute -bottom-4 -left-4 bg-green-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg transform -rotate-12">
                FIRST ONE FREE
              </div>
            </div>

          </div>
        </div>
      </div>

      <Analytics.ProductView
        data={{
          products: [
            {
              id: product.id,
              title: product.title,
              price: selectedVariant?.price?.amount || '0',
              vendor: product.vendor,
              variantId: selectedVariant?.id || '',
              variantTitle: selectedVariant?.title || '',
              quantity: 1,
            },
          ],
        }}
      />

      {/* Bundle Deals Section */}
      <div className="bg-gradient-to-r from-[#db8027] to-[#c4721f] rounded-2xl p-8 text-white relative overflow-hidden mt-12">
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-16 -translate-y-16"></div>
          <div className="absolute bottom-0 right-0 w-24 h-24 bg-white rounded-full translate-x-12 translate-y-12"></div>
          <div className="absolute top-1/2 left-1/3 w-16 h-16 bg-white rounded-full"></div>
        </div>

        <div className="relative z-10">
          <div className="text-center mb-8">
            <div className="inline-flex items-center space-x-2 mb-4">
              <span className="text-4xl">🎁</span>
              <h2 className="text-3xl font-bold">Upgrade Your Coffee Experience</h2>
            </div>
            <p className="text-xl opacity-90 mb-2">Get more variety and save with our curated bundles</p>
            <div className="inline-flex items-center space-x-2 bg-white/20 rounded-full px-4 py-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
              </svg>
              <span className="font-semibold">Save up to 25% with bundles</span>
            </div>
          </div>

          <BundleSection bundleProducts={bundleProducts} />
        </div>
      </div>

      {/* Cross-sell Section */}
      <div className="bg-gradient-to-br from-[#eeedc1] to-[#e5e2a8] py-12 relative overflow-hidden mt-12">
        {/* Background decorative elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-6 left-6 w-32 h-32 bg-[#3a5c5c] rounded-full"></div>
          <div className="absolute bottom-6 right-6 w-24 h-24 bg-[#db8027] rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-[#5d8e8e] rounded-full"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-[#3a5c5c] mb-4">☕ You Might Also Love</h2>
            <p className="text-xl text-[#3a5c5c]/80 max-w-2xl mx-auto">
              Discover more amazing flavors and convenient options from our collection
            </p>
          </div>

          <Suspense fallback={
            <div className="text-center py-4">
              <div className="inline-flex items-center space-x-2 text-[#3a5c5c]">
                <svg className="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="text-base">Loading recommendations...</span>
              </div>
            </div>
          }>
            <Await resolve={relatedProducts}>
              {(resolvedProducts) => (
                <CrossSellProducts
                  currentProduct={product as any}
                  allProducts={resolvedProducts?.products?.nodes || []}
                />
              )}
            </Await>
          </Suspense>
        </div>
      </div>
    </div>
  );
}

// GraphQL Queries
const SUBSCRIPTION_PROMO_PRODUCT_QUERY = `#graphql
  query SubscriptionPromoProduct(
    $id: ID!
    $selectedOptions: [SelectedOptionInput!]!
  ) {
    product(id: $id) {
      id
      title
      handle
      vendor
      description
      descriptionHtml
      encodedVariantExistence
      encodedVariantAvailability
      featuredImage {
        id
        altText
        url
        width
        height
      }
      images(first: 10) {
        nodes {
          id
          altText
          url
          width
          height
        }
      }
      options {
        name
        optionValues {
          name
          firstSelectableVariant {
            ...ProductVariant
          }
          swatch {
            color
            image {
              previewImage {
                url
              }
            }
          }
        }
      }
      selectedVariant: variantBySelectedOptions(selectedOptions: $selectedOptions) {
        ...ProductVariant
      }
      selectedOrFirstAvailableVariant(selectedOptions: $selectedOptions, ignoreUnknownOptions: true, caseInsensitiveMatch: true) {
        ...ProductVariant
      }
      adjacentVariants(selectedOptions: $selectedOptions) {
        ...ProductVariant
      }
      variants(first: 250) {
        nodes {
          ...ProductVariant
        }
      }
      sellingPlanGroups(first: 10) {
        nodes {
          name
          options {
            name
            values
          }
          sellingPlans(first: 10) {
            nodes {
              id
              name
              description
              options {
                name
                value
              }
              priceAdjustments {
                orderCount
                adjustmentValue {
                  ... on SellingPlanFixedAmountPriceAdjustment {
                    adjustmentAmount {
                      amount
                      currencyCode
                    }
                  }
                  ... on SellingPlanPercentagePriceAdjustment {
                    adjustmentPercentage
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  fragment ProductVariant on ProductVariant {
    availableForSale
    compareAtPrice {
      amount
      currencyCode
    }
    id
    image {
      __typename
      id
      url
      altText
      width
      height
    }
    price {
      amount
      currencyCode
    }
    product {
      title
      handle
    }
    selectedOptions {
      name
      value
    }
    sku
    title
    unitPrice {
      amount
      currencyCode
    }
  }
` as const;

const VARIANTS_QUERY = `#graphql
  query ProductVariants($id: ID!) {
    product(id: $id) {
      variants(first: 250) {
        nodes {
          ...ProductVariant
        }
      }
    }
  }
  fragment ProductVariant on ProductVariant {
    availableForSale
    compareAtPrice {
      amount
      currencyCode
    }
    id
    image {
      __typename
      id
      url
      altText
      width
      height
    }
    price {
      amount
      currencyCode
    }
    product {
      title
      handle
    }
    selectedOptions {
      name
      value
    }
    sku
    title
    unitPrice {
      amount
      currencyCode
    }
  }
` as const;

// Query for related products (cross-selling)
const RELATED_PRODUCTS_QUERY = `#graphql
  query RelatedProducts($first: Int!) {
    products(first: $first, query: "available_for_sale:true") {
      nodes {
        id
        title
        handle
        availableForSale
        featuredImage {
          id
          altText
          url
          width
          height
        }
        priceRange {
          minVariantPrice {
            amount
            currencyCode
          }
          maxVariantPrice {
            amount
            currencyCode
          }
        }
        variants(first: 1) {
          nodes {
            id
            availableForSale
            selectedOptions {
              name
              value
            }
            price {
              amount
              currencyCode
            }
            compareAtPrice {
              amount
              currencyCode
            }
          }
        }
      }
    }
  }
` as const;

// Query for specific bundle products
const BUNDLE_PRODUCTS_QUERY = `#graphql
  query BundleProducts($roastersBoxId: ID!, $blendBoxId: ID!) {
    roastersBox: product(id: $roastersBoxId) {
      id
      title
      handle
      availableForSale
      featuredImage {
        id
        altText
        url
        width
        height
      }
      priceRange {
        minVariantPrice {
          amount
          currencyCode
        }
      }
      variants(first: 1) {
        nodes {
          id
          availableForSale
          price {
            amount
            currencyCode
          }
        }
      }
    }
    blendBox: product(id: $blendBoxId) {
      id
      title
      handle
      availableForSale
      featuredImage {
        id
        altText
        url
        width
        height
      }
      priceRange {
        minVariantPrice {
          amount
          currencyCode
        }
      }
      variants(first: 1) {
        nodes {
          id
          availableForSale
          price {
            amount
            currencyCode
          }
        }
      }
    }
  }
` as const;
